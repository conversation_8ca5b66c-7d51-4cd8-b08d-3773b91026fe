# Android VPN Service with Flutter Integration

A comprehensive Android VPN service implementation using Kotlin that provides traffic interception, per-app filtering, and detailed logging capabilities. The service integrates seamlessly with Flutter through platform channels.

## Features

### Core VPN Functionality
- ✅ **VpnService.Builder Configuration**: Virtual IP and subnet setup
- ✅ **Traffic Interception**: Captures all outgoing device traffic
- ✅ **Packet Parsing**: IP, TCP, UDP header analysis
- ✅ **Per-App Filtering**: Block/allow traffic based on application rules
- ✅ **App Exclusion**: Exclude specific apps from VPN using `addDisallowedApplication()`
- ✅ **Background Service**: Persistent VPN with auto-restart capability
- ✅ **Android 10+ Support**: Compatible with API 29+ requirements

### Logging & Monitoring
- ✅ **Metadata Logging**: Source/destination IP, ports, protocol, app UID
- ✅ **Traffic Statistics**: Bytes transferred, packet counts, protocol breakdown
- ✅ **Real-time Monitoring**: Live traffic analysis and blocking
- ✅ **Export/Import**: JSON-based configuration management

### Flutter Integration
- ✅ **Platform Channels**: Bidirectional communication with Flutter
- ✅ **Real-time Updates**: VPN status and traffic updates
- ✅ **Configuration Management**: Dynamic rule updates from Flutter UI
- ✅ **Permission Handling**: VPN permission request flow

## Architecture

### Core Components

```
app/src/main/kotlin/com/example/securityapp/vpn/
├── SecurityVpnService.kt      # Main VPN service implementation
├── PacketParser.kt            # IP/TCP/UDP packet parsing utilities
├── VpnConfig.kt              # Configuration data classes
├── VpnLogger.kt              # Traffic logging and statistics
├── VpnRepository.kt          # Data management and persistence
└── VpnMethodChannel.kt       # Flutter platform channel integration
```

### Key Classes

#### SecurityVpnService
- Extends Android's `VpnService`
- Manages VPN interface lifecycle
- Handles packet processing and forwarding
- Provides foreground service with notifications

#### PacketParser
- Parses IPv4 and IPv6 packets
- Extracts network metadata (IPs, ports, protocols)
- Supports TCP, UDP, ICMP protocols

#### VpnLogger
- Real-time traffic logging
- Coroutine-based async processing
- Package name resolution from UID
- Traffic statistics generation

#### VpnRepository
- Configuration persistence
- App rule management
- SharedPreferences integration
- JSON import/export

## Configuration

### JSON Configuration Format

```json
{
  "appRules": [
    {
      "packageName": "com.android.chrome",
      "isBlocked": false,
      "isDisallowed": false,
      "description": "Chrome Browser"
    },
    {
      "packageName": "com.facebook.katana",
      "isBlocked": true,
      "isDisallowed": false,
      "description": "Facebook App"
    }
  ],
  "logTraffic": true,
  "virtualIp": "********",
  "virtualSubnet": "10.0.0.0/24",
  "dnsServers": ["*******", "*******"],
  "mtu": 1500
}
```

### App Rule Types
- **isBlocked**: Block traffic from this app
- **isDisallowed**: Exclude app from VPN entirely (uses `addDisallowedApplication()`)

## Usage

### Android/Kotlin

```kotlin
// Start VPN service
val intent = Intent(context, SecurityVpnService::class.java).apply {
    action = "START_VPN"
    putExtra("config", configJson)
}
context.startForegroundService(intent)

// Update app rule
repository.updateAppRule("com.example.app", isBlocked = true, isDisallowed = false)
```

### Flutter

```dart
// Request VPN permission
bool granted = await VpnService.requestVpnPermission();

// Start VPN
bool success = await VpnService.startVpn(config: configJson);

// Update app rule
await VpnService.updateAppRule(
  packageName: 'com.example.app',
  isBlocked: true,
  isDisallowed: false,
);

// Get traffic logs
List<Map<String, dynamic>> logs = await VpnService.getTrafficLogs();

// Listen for VPN status updates
VpnService.setMethodCallHandler((MethodCall call) async {
  if (call.method == 'onVpnStatusChanged') {
    final status = VpnStatus.fromMap(call.arguments);
    // Handle status update
  }
});
```

## Permissions

Required permissions in `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## Dependencies

### Android (build.gradle.kts)
```kotlin
dependencies {
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0")
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-service:2.7.0")
}
```

## Installation

1. **Clone the repository**
2. **Open in Android Studio**
3. **Sync Gradle dependencies**
4. **Run on Android device** (VPN services don't work in emulators)

## Testing

### Manual Testing
1. Install the app on a physical Android device
2. Grant VPN permission when prompted
3. Start the VPN service
4. Configure app blocking rules
5. Monitor traffic logs
6. Verify blocked apps cannot access network

### Automated Testing
```bash
# Run Android unit tests
./gradlew test

# Run Android instrumentation tests
./gradlew connectedAndroidTest
```

## Security Considerations

- **Root Access**: Not required - uses Android VpnService API
- **Traffic Encryption**: Implement additional encryption if needed
- **DNS Leaks**: Configure DNS servers properly
- **App Exclusion**: Critical apps (system, VPN client) should be excluded
- **Permission Model**: Follows Android VPN permission requirements

## Limitations

- **Emulator Support**: VPN services don't work in Android emulators
- **Root Traffic**: Some system-level traffic may bypass VPN
- **IPv6 Support**: Basic implementation - may need enhancement
- **Deep Packet Inspection**: Limited to header analysis
- **Performance**: Packet processing may impact device performance

## Troubleshooting

### Common Issues

1. **VPN Won't Start**
   - Check VPN permission granted
   - Verify no other VPN is active
   - Check Android version (API 29+ required)

2. **Apps Not Blocked**
   - Verify app rules configuration
   - Check if app is excluded via `isDisallowed`
   - Restart VPN after rule changes

3. **Performance Issues**
   - Reduce logging frequency
   - Optimize packet processing
   - Check device memory usage

### Debug Logging
```kotlin
// Enable debug logging
Log.d("VpnService", "Debug message")

// Check VPN status
adb logcat | grep VpnService
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This VPN implementation is for educational and development purposes. Ensure compliance with local laws and regulations when deploying VPN services. The authors are not responsible for misuse of this software.
