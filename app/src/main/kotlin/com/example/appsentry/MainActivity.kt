package com.example.appsentry

import android.content.Intent
import com.example.appsentry.vpn.VpnMethodChannel
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity : FlutterActivity() {

    private var vpnMethodChannel: VpnMethodChannel? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Initialize VPN method channel
        vpnMethodChannel = VpnMethodChannel(this, this, flutterEngine)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // Forward VPN permission result to method channel
        vpnMethodChannel?.onActivityResult(requestCode, resultCode)
    }

    override fun onDestroy() {
        super.onDestroy()
        vpnMethodChannel?.cleanup()
    }
}
