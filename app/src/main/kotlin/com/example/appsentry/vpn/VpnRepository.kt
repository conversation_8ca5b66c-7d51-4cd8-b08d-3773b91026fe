package com.example.appsentry.vpn

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * Repository for managing VPN configuration and app data
 */
class VpnRepository(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "vpn_preferences"
        private const val KEY_VPN_CONFIG = "vpn_config"
        private const val KEY_AUTO_START = "auto_start_vpn"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    private val configManager = VpnConfigManager()
    private val packageManager = context.packageManager
    
    private val _installedApps = MutableStateFlow<List<AppInfo>>(emptyList())
    val installedApps: StateFlow<List<AppInfo>> = _installedApps.asStateFlow()
    
    private val _vpnConfig = MutableStateFlow(configManager.getDefaultConfig())
    val vpnConfig: StateFlow<VpnConfiguration> = _vpnConfig.asStateFlow()
    
    /**
     * Data class for app information
     */
    data class AppInfo(
        val packageName: String,
        val appName: String,
        val isSystemApp: Boolean,
        val uid: Int,
        val versionName: String?,
        val versionCode: Long,
        val isBlocked: Boolean = false,
        val isDisallowed: Boolean = false
    )
    
    init {
        loadVpnConfig()
    }
    
    /**
     * Load VPN configuration from SharedPreferences
     */
    private fun loadVpnConfig() {
        val configJson = sharedPreferences.getString(KEY_VPN_CONFIG, null)
        if (configJson != null) {
            _vpnConfig.value = configManager.parseConfig(configJson)
        }
    }
    
    /**
     * Save VPN configuration to SharedPreferences
     */
    suspend fun saveVpnConfig(config: VpnConfiguration) {
        withContext(Dispatchers.IO) {
            val configJson = configManager.configToJson(config)
            sharedPreferences.edit()
                .putString(KEY_VPN_CONFIG, configJson)
                .apply()
            
            _vpnConfig.value = config
        }
    }
    
    /**
     * Load all installed applications
     */
    suspend fun loadInstalledApps() {
        withContext(Dispatchers.IO) {
            try {
                val packages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
                val currentConfig = _vpnConfig.value
                
                val appInfoList = packages.map { appInfo ->
                    val packageInfo = try {
                        packageManager.getPackageInfo(appInfo.packageName, 0)
                    } catch (e: Exception) {
                        null
                    }
                    
                    val appRule = currentConfig.appRules.find { it.packageName == appInfo.packageName }
                    
                    AppInfo(
                        packageName = appInfo.packageName,
                        appName = getAppName(appInfo),
                        isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0,
                        uid = appInfo.uid,
                        versionName = packageInfo?.versionName,
                        versionCode = packageInfo?.longVersionCode ?: 0L,
                        isBlocked = appRule?.isBlocked ?: false,
                        isDisallowed = appRule?.isDisallowed ?: false
                    )
                }.sortedBy { it.appName.lowercase() }
                
                _installedApps.value = appInfoList
            } catch (e: Exception) {
                // Handle error - could emit to error state
            }
        }
    }
    
    /**
     * Get app name from ApplicationInfo
     */
    private fun getAppName(appInfo: ApplicationInfo): String {
        return try {
            packageManager.getApplicationLabel(appInfo).toString()
        } catch (e: Exception) {
            appInfo.packageName
        }
    }
    
    /**
     * Update app rule (block/unblock/disallow)
     */
    suspend fun updateAppRule(packageName: String, isBlocked: Boolean, isDisallowed: Boolean = false) {
        val currentConfig = _vpnConfig.value
        val updatedRules = currentConfig.appRules.toMutableList()
        
        // Remove existing rule for this package
        updatedRules.removeAll { it.packageName == packageName }
        
        // Add new rule
        val appInfo = _installedApps.value.find { it.packageName == packageName }
        val appName = appInfo?.appName ?: packageName
        
        updatedRules.add(
            AppRule(
                packageName = packageName,
                isBlocked = isBlocked,
                isDisallowed = isDisallowed,
                description = appName
            )
        )
        
        val newConfig = currentConfig.copy(appRules = updatedRules)
        saveVpnConfig(newConfig)
        
        // Update installed apps list
        updateInstalledAppsList()
    }
    
    /**
     * Remove app rule
     */
    suspend fun removeAppRule(packageName: String) {
        val currentConfig = _vpnConfig.value
        val updatedRules = currentConfig.appRules.filter { it.packageName != packageName }
        
        val newConfig = currentConfig.copy(appRules = updatedRules)
        saveVpnConfig(newConfig)
        
        // Update installed apps list
        updateInstalledAppsList()
    }
    
    /**
     * Update installed apps list with current rules
     */
    private suspend fun updateInstalledAppsList() {
        val currentApps = _installedApps.value
        val currentConfig = _vpnConfig.value
        
        val updatedApps = currentApps.map { app ->
            val rule = currentConfig.appRules.find { it.packageName == app.packageName }
            app.copy(
                isBlocked = rule?.isBlocked ?: false,
                isDisallowed = rule?.isDisallowed ?: false
            )
        }
        
        _installedApps.value = updatedApps
    }
    
    /**
     * Get apps by category
     */
    fun getSystemApps(): List<AppInfo> = _installedApps.value.filter { it.isSystemApp }
    fun getUserApps(): List<AppInfo> = _installedApps.value.filter { !it.isSystemApp }
    fun getBlockedApps(): List<AppInfo> = _installedApps.value.filter { it.isBlocked }
    fun getDisallowedApps(): List<AppInfo> = _installedApps.value.filter { it.isDisallowed }
    
    /**
     * Search apps by name or package name
     */
    fun searchApps(query: String): List<AppInfo> {
        val lowercaseQuery = query.lowercase()
        return _installedApps.value.filter { app ->
            app.appName.lowercase().contains(lowercaseQuery) ||
            app.packageName.lowercase().contains(lowercaseQuery)
        }
    }
    
    /**
     * Update VPN settings
     */
    suspend fun updateVpnSettings(
        logTraffic: Boolean? = null,
        virtualIp: String? = null,
        virtualSubnet: String? = null,
        dnsServers: List<String>? = null,
        mtu: Int? = null
    ) {
        val currentConfig = _vpnConfig.value
        val newConfig = currentConfig.copy(
            logTraffic = logTraffic ?: currentConfig.logTraffic,
            virtualIp = virtualIp ?: currentConfig.virtualIp,
            virtualSubnet = virtualSubnet ?: currentConfig.virtualSubnet,
            dnsServers = dnsServers ?: currentConfig.dnsServers,
            mtu = mtu ?: currentConfig.mtu
        )
        
        saveVpnConfig(newConfig)
    }
    
    /**
     * Export configuration as JSON
     */
    fun exportConfig(): String {
        return configManager.configToJson(_vpnConfig.value)
    }
    
    /**
     * Import configuration from JSON
     */
    suspend fun importConfig(configJson: String): Boolean {
        return try {
            val config = configManager.parseConfig(configJson)
            saveVpnConfig(config)
            updateInstalledAppsList()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Reset to default configuration
     */
    suspend fun resetToDefault() {
        val defaultConfig = configManager.getDefaultConfig()
        saveVpnConfig(defaultConfig)
        updateInstalledAppsList()
    }
    
    /**
     * Auto-start VPN preference
     */
    fun setAutoStartVpn(autoStart: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_START, autoStart)
            .apply()
    }
    
    fun shouldAutoStartVpn(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_START, false)
    }
}
