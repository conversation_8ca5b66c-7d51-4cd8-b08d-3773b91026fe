package com.example.appsentry.vpn

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * VPN Logger for network traffic metadata
 */
class VpnLogger(private val context: Context) {
    
    companion object {
        private const val TAG = "VpnLogger"
        private const val MAX_LOGS = 1000
    }
    
    private val logQueue = ConcurrentLinkedQueue<NetworkLog>()
    private val logChannel = Channel<NetworkLog>(Channel.UNLIMITED)
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _logs = MutableStateFlow<List<NetworkLog>>(emptyList())
    val logs: StateFlow<List<NetworkLog>> = _logs.asStateFlow()
    
    private val packageManager = context.packageManager
    private val packageNameCache = mutableMapOf<Int, String>()
    
    init {
        startLogProcessor()
    }
    
    /**
     * Log network packet metadata
     */
    fun logPacket(
        packetInfo: PacketParser.PacketInfo,
        appUid: Int,
        isBlocked: Boolean = false
    ) {
        val packageName = getPackageNameFromUid(appUid)
        
        val networkLog = NetworkLog(
            timestamp = System.currentTimeMillis(),
            sourceIp = packetInfo.sourceIp,
            destinationIp = packetInfo.destinationIp,
            sourcePort = packetInfo.sourcePort,
            destinationPort = packetInfo.destinationPort,
            protocol = PacketParser().getProtocolName(packetInfo.protocol),
            appUid = appUid,
            packageName = packageName,
            dataSize = packetInfo.dataLength,
            isBlocked = isBlocked
        )
        
        // Send to channel for processing
        logChannel.trySend(networkLog)
        
        // Log to Android Log for debugging
        Log.d(TAG, "Packet: ${packetInfo.sourceIp}:${packetInfo.sourcePort} -> " +
                "${packetInfo.destinationIp}:${packetInfo.destinationPort} " +
                "(${PacketParser().getProtocolName(packetInfo.protocol)}) " +
                "App: $packageName (UID: $appUid) " +
                "Size: ${packetInfo.dataLength} bytes " +
                "Blocked: $isBlocked")
    }
    
    /**
     * Start the log processor coroutine
     */
    private fun startLogProcessor() {
        coroutineScope.launch {
            for (log in logChannel) {
                processLog(log)
            }
        }
    }
    
    /**
     * Process and store the log entry
     */
    private suspend fun processLog(log: NetworkLog) {
        withContext(Dispatchers.Main) {
            logQueue.offer(log)
            
            // Maintain maximum log size
            while (logQueue.size > MAX_LOGS) {
                logQueue.poll()
            }
            
            // Update StateFlow
            _logs.value = logQueue.toList().sortedByDescending { it.timestamp }
        }
    }
    
    /**
     * Get package name from UID with caching
     */
    fun getPackageNameFromUid(uid: Int): String {
        return packageNameCache.getOrPut(uid) {
            try {
                val packages = packageManager.getPackagesForUid(uid)
                packages?.firstOrNull() ?: "Unknown (UID: $uid)"
            } catch (e: Exception) {
                "Unknown (UID: $uid)"
            }
        }
    }
    
    /**
     * Get logs for a specific package
     */
    fun getLogsForPackage(packageName: String): List<NetworkLog> {
        return logQueue.filter { it.packageName == packageName }
    }
    
    /**
     * Get logs for a specific time range
     */
    fun getLogsInTimeRange(startTime: Long, endTime: Long): List<NetworkLog> {
        return logQueue.filter { it.timestamp in startTime..endTime }
    }
    
    /**
     * Get blocked traffic logs
     */
    fun getBlockedLogs(): List<NetworkLog> {
        return logQueue.filter { it.isBlocked }
    }
    
    /**
     * Get traffic statistics
     */
    fun getTrafficStats(): TrafficStats {
        val logs = logQueue.toList()
        val totalPackets = logs.size
        val blockedPackets = logs.count { it.isBlocked }
        val totalBytes = logs.sumOf { it.dataSize.toLong() }
        val uniqueApps = logs.map { it.packageName }.distinct().size
        val protocolStats = logs.groupBy { it.protocol }
            .mapValues { it.value.size }
        
        return TrafficStats(
            totalPackets = totalPackets,
            blockedPackets = blockedPackets,
            allowedPackets = totalPackets - blockedPackets,
            totalBytes = totalBytes,
            uniqueApps = uniqueApps,
            protocolStats = protocolStats,
            timeRange = if (logs.isNotEmpty()) {
                logs.minOf { it.timestamp } to logs.maxOf { it.timestamp }
            } else {
                0L to 0L
            }
        )
    }
    
    /**
     * Clear all logs
     */
    fun clearLogs() {
        coroutineScope.launch {
            logQueue.clear()
            packageNameCache.clear()
            _logs.value = emptyList()
        }
    }
    
    /**
     * Export logs as JSON string
     */
    fun exportLogsAsJson(): String {
        val logs = logQueue.toList()
        return VpnConfigManager().configToJson(
            VpnConfiguration().copy(
                appRules = logs.map { log ->
                    AppRule(
                        packageName = log.packageName,
                        isBlocked = log.isBlocked,
                        description = "Auto-generated from logs"
                    )
                }.distinctBy { it.packageName }
            )
        )
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        coroutineScope.cancel()
        logChannel.close()
    }
}

/**
 * Traffic statistics data class
 */
data class TrafficStats(
    val totalPackets: Int,
    val blockedPackets: Int,
    val allowedPackets: Int,
    val totalBytes: Long,
    val uniqueApps: Int,
    val protocolStats: Map<String, Int>,
    val timeRange: Pair<Long, Long>
)
