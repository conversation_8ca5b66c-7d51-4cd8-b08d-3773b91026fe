package com.example.appsentry.vpn

import java.net.InetAddress
import java.nio.ByteBuffer

/**
 * Utility class for parsing IP packets and extracting network metadata
 */
class PacketParser {
    
    companion object {
        const val IP_VERSION_4 = 4
        const val IP_VERSION_6 = 6
        const val PROTOCOL_TCP = 6
        const val PROTOCOL_UDP = 17
        const val PROTOCOL_ICMP = 1
        const val PROTOCOL_ICMPV6 = 58
    }
    
    data class PacketInfo(
        val version: Int,
        val protocol: Int,
        val sourceIp: String,
        val destinationIp: String,
        val sourcePort: Int = 0,
        val destinationPort: Int = 0,
        val dataLength: Int,
        val headerLength: Int,
        val isValid: Boolean = true
    )
    
    /**
     * Parse an IP packet from byte array
     */
    fun parsePacket(packetData: ByteArray): PacketInfo? {
        if (packetData.isEmpty()) return null
        
        return try {
            val buffer = ByteBuffer.wrap(packetData)
            val versionAndHeaderLength = buffer.get().toInt() and 0xFF
            val version = (versionAndHeaderLength shr 4) and 0x0F
            
            when (version) {
                IP_VERSION_4 -> parseIPv4Packet(buffer, versionAndHeaderLength)
                IP_VERSION_6 -> parseIPv6Packet(buffer)
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Parse IPv4 packet
     */
    private fun parseIPv4Packet(buffer: ByteBuffer, versionAndHeaderLength: Int): PacketInfo? {
        try {
            buffer.rewind()
            
            // Skip version and header length (already read)
            buffer.get()
            
            // Skip Type of Service
            buffer.get()
            
            // Total Length
            val totalLength = buffer.short.toInt() and 0xFFFF
            
            // Skip Identification, Flags, Fragment Offset
            buffer.getInt()
            
            // TTL
            buffer.get()
            
            // Protocol
            val protocol = buffer.get().toInt() and 0xFF
            
            // Skip Header Checksum
            buffer.getShort()
            
            // Source IP
            val sourceIpBytes = ByteArray(4)
            buffer.get(sourceIpBytes)
            val sourceIp = InetAddress.getByAddress(sourceIpBytes).hostAddress ?: ""
            
            // Destination IP
            val destIpBytes = ByteArray(4)
            buffer.get(destIpBytes)
            val destinationIp = InetAddress.getByAddress(destIpBytes).hostAddress ?: ""
            
            val headerLength = (versionAndHeaderLength and 0x0F) * 4
            val dataLength = totalLength - headerLength
            
            // Parse ports for TCP/UDP
            var sourcePort = 0
            var destinationPort = 0
            
            if (protocol == PROTOCOL_TCP || protocol == PROTOCOL_UDP) {
                if (buffer.remaining() >= 4) {
                    sourcePort = buffer.short.toInt() and 0xFFFF
                    destinationPort = buffer.short.toInt() and 0xFFFF
                }
            }
            
            return PacketInfo(
                version = IP_VERSION_4,
                protocol = protocol,
                sourceIp = sourceIp,
                destinationIp = destinationIp,
                sourcePort = sourcePort,
                destinationPort = destinationPort,
                dataLength = dataLength,
                headerLength = headerLength
            )
            
        } catch (e: Exception) {
            return null
        }
    }
    
    /**
     * Parse IPv6 packet (basic implementation)
     */
    private fun parseIPv6Packet(buffer: ByteBuffer): PacketInfo? {
        try {
            buffer.rewind()
            
            // Skip Version, Traffic Class, Flow Label (4 bytes)
            buffer.getInt()
            
            // Payload Length
            val payloadLength = buffer.short.toInt() and 0xFFFF
            
            // Next Header (Protocol)
            val protocol = buffer.get().toInt() and 0xFF
            
            // Skip Hop Limit
            buffer.get()
            
            // Source IP (16 bytes)
            val sourceIpBytes = ByteArray(16)
            buffer.get(sourceIpBytes)
            val sourceIp = InetAddress.getByAddress(sourceIpBytes).hostAddress ?: ""
            
            // Destination IP (16 bytes)
            val destIpBytes = ByteArray(16)
            buffer.get(destIpBytes)
            val destinationIp = InetAddress.getByAddress(destIpBytes).hostAddress ?: ""
            
            // Parse ports for TCP/UDP
            var sourcePort = 0
            var destinationPort = 0
            
            if (protocol == PROTOCOL_TCP || protocol == PROTOCOL_UDP) {
                if (buffer.remaining() >= 4) {
                    sourcePort = buffer.short.toInt() and 0xFFFF
                    destinationPort = buffer.short.toInt() and 0xFFFF
                }
            }
            
            return PacketInfo(
                version = IP_VERSION_6,
                protocol = protocol,
                sourceIp = sourceIp,
                destinationIp = destinationIp,
                sourcePort = sourcePort,
                destinationPort = destinationPort,
                dataLength = payloadLength,
                headerLength = 40 // IPv6 header is always 40 bytes
            )
            
        } catch (e: Exception) {
            return null
        }
    }
    
    /**
     * Get protocol name from protocol number
     */
    fun getProtocolName(protocol: Int): String {
        return when (protocol) {
            PROTOCOL_TCP -> "TCP"
            PROTOCOL_UDP -> "UDP"
            PROTOCOL_ICMP -> "ICMP"
            PROTOCOL_ICMPV6 -> "ICMPv6"
            else -> "Unknown($protocol)"
        }
    }
    
    /**
     * Check if packet should be blocked based on rules
     */
    fun shouldBlockPacket(packetInfo: PacketInfo, appRules: List<AppRule>, packageName: String): Boolean {
        val rule = appRules.find { it.packageName == packageName }
        return rule?.isBlocked ?: false
    }
}
