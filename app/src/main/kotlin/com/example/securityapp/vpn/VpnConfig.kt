package com.example.securityapp.vpn

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * Configuration data classes for VPN service
 */

@Serializable
data class AppRule(
    val packageName: String,
    val isBlocked: Boolean,
    val isDisallowed: Boolean = false, // Exclude from VPN entirely
    val description: String = ""
)

@Serializable
data class VpnConfiguration(
    val appRules: List<AppRule> = emptyList(),
    val logTraffic: Boolean = true,
    val virtualIp: String = "********",
    val virtualSubnet: String = "10.0.0.0/24",
    val dnsServers: List<String> = listOf("*******", "*******"),
    val mtu: Int = 1500
)

@Serializable
data class NetworkLog(
    val timestamp: Long,
    val sourceIp: String,
    val destinationIp: String,
    val sourcePort: Int,
    val destinationPort: Int,
    val protocol: String, // TCP, UDP, ICMP
    val appUid: Int,
    val packageName: String = "",
    val dataSize: Int,
    val isBlocked: Boolean = false
)

/**
 * VPN Configuration Manager
 */
class VpnConfigManager {
    private val json = Json { 
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    companion object {
        const val DEFAULT_CONFIG = """
        {
            "appRules": [
                {
                    "packageName": "com.android.chrome",
                    "isBlocked": false,
                    "description": "Chrome Browser"
                },
                {
                    "packageName": "com.facebook.katana",
                    "isBlocked": true,
                    "description": "Facebook App"
                }
            ],
            "logTraffic": true,
            "virtualIp": "********",
            "virtualSubnet": "10.0.0.0/24",
            "dnsServers": ["*******", "*******"],
            "mtu": 1500
        }
        """
    }
    
    fun parseConfig(configJson: String): VpnConfiguration {
        return try {
            json.decodeFromString<VpnConfiguration>(configJson)
        } catch (e: Exception) {
            // Return default config if parsing fails
            json.decodeFromString<VpnConfiguration>(DEFAULT_CONFIG)
        }
    }
    
    fun configToJson(config: VpnConfiguration): String {
        return json.encodeToString(VpnConfiguration.serializer(), config)
    }
    
    fun getDefaultConfig(): VpnConfiguration {
        return json.decodeFromString<VpnConfiguration>(DEFAULT_CONFIG)
    }
}

/**
 * VPN State Management
 */
enum class VpnState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}

data class VpnStatus(
    val state: VpnState,
    val connectedTime: Long = 0,
    val bytesReceived: Long = 0,
    val bytesSent: Long = 0,
    val packetsProcessed: Long = 0,
    val errorMessage: String? = null
)
