package com.example.securityapp.vpn

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.content.pm.PackageManager
import android.net.VpnService
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.ParcelFileDescriptor
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.securityapp.MainActivity
import com.example.securityapp.R
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.FileInputStream
import java.io.FileOutputStream
import java.net.InetSocketAddress
import java.nio.ByteBuffer
import java.nio.channels.DatagramChannel

/**
 * Main VPN Service implementation
 */
class SecurityVpnService : VpnService() {
    
    companion object {
        private const val TAG = "SecurityVpnService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "vpn_service_channel"
        private const val BUFFER_SIZE = 32767
        private const val VIRTUAL_IP = "********"
        private const val VIRTUAL_SUBNET = "10.0.0.0"
        private const val SUBNET_PREFIX = 24
    }
    
    // Service binding
    private val binder = VpnServiceBinder()
    
    // VPN components
    private var vpnInterface: ParcelFileDescriptor? = null
    private var inputStream: FileInputStream? = null
    private var outputStream: FileOutputStream? = null
    
    // Configuration and logging
    private lateinit var vpnConfig: VpnConfiguration
    private lateinit var vpnLogger: VpnLogger
    private val packetParser = PacketParser()
    
    // Coroutines
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var packetProcessingJob: Job? = null
    
    // State management
    private val _vpnStatus = MutableStateFlow(
        VpnStatus(
            state = VpnState.DISCONNECTED,
            connectedTime = 0,
            bytesReceived = 0,
            bytesSent = 0,
            packetsProcessed = 0
        )
    )
    val vpnStatus: StateFlow<VpnStatus> = _vpnStatus.asStateFlow()
    
    // Statistics
    private var startTime: Long = 0
    private var bytesReceived: Long = 0
    private var bytesSent: Long = 0
    private var packetsProcessed: Long = 0
    
    inner class VpnServiceBinder : Binder() {
        fun getService(): SecurityVpnService = this@SecurityVpnService
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "VPN Service created")
        
        vpnLogger = VpnLogger(this)
        vpnConfig = VpnConfigManager().getDefaultConfig()
        
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "VPN Service start command received")
        
        when (intent?.action) {
            "START_VPN" -> {
                val configJson = intent.getStringExtra("config")
                if (configJson != null) {
                    vpnConfig = VpnConfigManager().parseConfig(configJson)
                }
                startVpn()
            }
            "STOP_VPN" -> stopVpn()
            else -> startVpn()
        }
        
        return START_STICKY // Restart if killed
    }
    
    /**
     * Start the VPN connection
     */
    fun startVpn() {
        if (_vpnStatus.value.state == VpnState.CONNECTED || 
            _vpnStatus.value.state == VpnState.CONNECTING) {
            Log.w(TAG, "VPN already started or starting")
            return
        }
        
        updateVpnStatus(VpnState.CONNECTING)
        
        serviceScope.launch {
            try {
                setupVpnInterface()
                startForegroundService()
                startPacketProcessing()
                
                startTime = System.currentTimeMillis()
                updateVpnStatus(VpnState.CONNECTED)
                
                Log.i(TAG, "VPN started successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start VPN", e)
                updateVpnStatus(VpnState.ERROR, e.message)
                stopSelf()
            }
        }
    }
    
    /**
     * Stop the VPN connection
     */
    fun stopVpn() {
        updateVpnStatus(VpnState.DISCONNECTING)
        
        packetProcessingJob?.cancel()
        
        try {
            inputStream?.close()
            outputStream?.close()
            vpnInterface?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error closing VPN interface", e)
        }
        
        inputStream = null
        outputStream = null
        vpnInterface = null
        
        updateVpnStatus(VpnState.DISCONNECTED)
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
        
        Log.i(TAG, "VPN stopped")
    }
    
    /**
     * Setup VPN interface using VpnService.Builder
     */
    private fun setupVpnInterface() {
        val builder = Builder()
            .setSession("SecurityApp VPN")
            .addAddress(vpnConfig.virtualIp, SUBNET_PREFIX)
            .addRoute("0.0.0.0", 0) // Route all traffic through VPN
            .setMtu(vpnConfig.mtu)
        
        // Add DNS servers
        vpnConfig.dnsServers.forEach { dns ->
            builder.addDnsServer(dns)
        }
        
        // Configure disallowed applications
        vpnConfig.appRules.filter { it.isDisallowed }.forEach { rule ->
            try {
                builder.addDisallowedApplication(rule.packageName)
                Log.d(TAG, "Disallowed app: ${rule.packageName}")
            } catch (e: PackageManager.NameNotFoundException) {
                Log.w(TAG, "Package not found: ${rule.packageName}")
            }
        }
        
        // Always exclude our own app to prevent loops
        try {
            builder.addDisallowedApplication(packageName)
        } catch (e: Exception) {
            Log.w(TAG, "Could not exclude own package", e)
        }
        
        vpnInterface = builder.establish()
            ?: throw IllegalStateException("Failed to establish VPN interface")
        
        inputStream = FileInputStream(vpnInterface!!.fileDescriptor)
        outputStream = FileOutputStream(vpnInterface!!.fileDescriptor)
        
        Log.d(TAG, "VPN interface established")
    }
    
    /**
     * Start foreground service with notification
     */
    private fun startForegroundService() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
    }
    
    /**
     * Start packet processing in background
     */
    private fun startPacketProcessing() {
        packetProcessingJob = serviceScope.launch {
            val buffer = ByteBuffer.allocate(BUFFER_SIZE)
            
            while (isActive && inputStream != null) {
                try {
                    buffer.clear()
                    val length = inputStream!!.read(buffer.array())
                    
                    if (length > 0) {
                        processPacket(buffer.array(), length)
                        packetsProcessed++
                        bytesReceived += length
                        
                        // Update status periodically
                        if (packetsProcessed % 100 == 0L) {
                            updateVpnStatusStats()
                        }
                    }
                } catch (e: Exception) {
                    if (isActive) {
                        Log.e(TAG, "Error processing packet", e)
                    }
                    break
                }
            }
        }
    }
    
    /**
     * Process individual packet
     */
    private suspend fun processPacket(packetData: ByteArray, length: Int) {
        val packet = ByteArray(length)
        System.arraycopy(packetData, 0, packet, 0, length)
        
        val packetInfo = packetParser.parsePacket(packet)
        if (packetInfo == null || !packetInfo.isValid) {
            return
        }
        
        // Get app UID (simplified - in real implementation, you'd need to 
        // correlate with connection tracking)
        val appUid = android.os.Process.myUid() // Placeholder
        
        // Check if packet should be blocked
        val packageName = vpnLogger.getPackageNameFromUid(appUid)
        val shouldBlock = packetParser.shouldBlockPacket(packetInfo, vpnConfig.appRules, packageName)
        
        // Log the packet if logging is enabled
        if (vpnConfig.logTraffic) {
            vpnLogger.logPacket(packetInfo, appUid, shouldBlock)
        }
        
        // Forward packet if not blocked
        if (!shouldBlock) {
            forwardPacket(packet)
            bytesSent += length
        }
    }
    
    /**
     * Forward packet to destination (simplified implementation)
     */
    private suspend fun forwardPacket(packet: ByteArray) {
        withContext(Dispatchers.IO) {
            try {
                outputStream?.write(packet)
                outputStream?.flush()
            } catch (e: Exception) {
                Log.e(TAG, "Error forwarding packet", e)
            }
        }
    }

    /**
     * Update VPN status
     */
    private fun updateVpnStatus(state: VpnState, errorMessage: String? = null) {
        _vpnStatus.value = _vpnStatus.value.copy(
            state = state,
            errorMessage = errorMessage
        )
    }

    /**
     * Update VPN status with current statistics
     */
    private fun updateVpnStatusStats() {
        val currentTime = System.currentTimeMillis()
        val connectedTime = if (startTime > 0) currentTime - startTime else 0

        _vpnStatus.value = _vpnStatus.value.copy(
            connectedTime = connectedTime,
            bytesReceived = bytesReceived,
            bytesSent = bytesSent,
            packetsProcessed = packetsProcessed
        )
    }

    /**
     * Create notification channel for Android O+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Security VPN Service notifications"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Create notification for foreground service
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val stopIntent = Intent(this, SecurityVpnService::class.java).apply {
            action = "STOP_VPN"
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Security VPN Active")
            .setContentText("Protecting your network traffic")
            .setSmallIcon(android.R.drawable.ic_lock_lock)
            .setContentIntent(pendingIntent)
            .addAction(
                android.R.drawable.ic_menu_close_clear_cancel,
                "Stop VPN",
                stopPendingIntent
            )
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * Update VPN configuration
     */
    fun updateConfig(newConfig: VpnConfiguration) {
        vpnConfig = newConfig
        Log.d(TAG, "VPN configuration updated")
    }

    /**
     * Get current VPN configuration
     */
    fun getConfig(): VpnConfiguration = vpnConfig

    /**
     * Get VPN logger instance
     */
    fun getLogger(): VpnLogger = vpnLogger

    /**
     * Check if VPN is currently connected
     */
    fun isConnected(): Boolean = _vpnStatus.value.state == VpnState.CONNECTED

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "VPN Service destroyed")

        serviceScope.cancel()
        vpnLogger.cleanup()

        try {
            inputStream?.close()
            outputStream?.close()
            vpnInterface?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up VPN service", e)
        }
    }

    override fun onRevoke() {
        super.onRevoke()
        Log.w(TAG, "VPN permission revoked")
        stopVpn()
    }
}
