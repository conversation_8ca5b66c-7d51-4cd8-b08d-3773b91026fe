package com.example.securityapp.vpn

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.net.VpnService
import android.os.IBinder
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect

/**
 * Flutter platform channel for VPN service integration
 */
class VpnMethodChannel(
    private val context: Context,
    private val activity: Activity?,
    flutterEngine: FlutterEngine
) : MethodChannel.MethodCallHandler {
    
    companion object {
        private const val TAG = "VpnMethodChannel"
        private const val CHANNEL_NAME = "com.example.securityapp/vpn"
        private const val VPN_REQUEST_CODE = 1001
    }
    
    private val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME)
    private val repository = VpnRepository(context)
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // Service connection
    private var vpnService: SecurityVpnService? = null
    private var isServiceBound = false
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as SecurityVpnService.VpnServiceBinder
            vpnService = binder.getService()
            isServiceBound = true
            
            // Start observing VPN status
            observeVpnStatus()
            
            Log.d(TAG, "VPN service connected")
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            vpnService = null
            isServiceBound = false
            Log.d(TAG, "VPN service disconnected")
        }
    }
    
    init {
        methodChannel.setMethodCallHandler(this)
        bindToVpnService()
        
        // Load installed apps on initialization
        scope.launch {
            repository.loadInstalledApps()
        }
    }
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "requestVpnPermission" -> requestVpnPermission(result)
            "startVpn" -> startVpn(call, result)
            "stopVpn" -> stopVpn(result)
            "getVpnStatus" -> getVpnStatus(result)
            "updateAppRule" -> updateAppRule(call, result)
            "getInstalledApps" -> getInstalledApps(result)
            "getVpnConfig" -> getVpnConfig(result)
            "updateVpnConfig" -> updateVpnConfig(call, result)
            "getTrafficLogs" -> getTrafficLogs(result)
            "getTrafficStats" -> getTrafficStats(result)
            "clearLogs" -> clearLogs(result)
            "exportConfig" -> exportConfig(result)
            "importConfig" -> importConfig(call, result)
            "searchApps" -> searchApps(call, result)
            else -> result.notImplemented()
        }
    }
    
    /**
     * Request VPN permission from user
     */
    private fun requestVpnPermission(result: MethodChannel.Result) {
        val intent = VpnService.prepare(context)
        if (intent != null && activity != null) {
            activity.startActivityForResult(intent, VPN_REQUEST_CODE)
            result.success(false) // Permission not yet granted
        } else {
            result.success(true) // Permission already granted
        }
    }
    
    /**
     * Start VPN service
     */
    private fun startVpn(call: MethodCall, result: MethodChannel.Result) {
        val configJson = call.argument<String>("config")
        
        val intent = Intent(context, SecurityVpnService::class.java).apply {
            action = "START_VPN"
            if (configJson != null) {
                putExtra("config", configJson)
            }
        }
        
        try {
            context.startForegroundService(intent)
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start VPN service", e)
            result.error("START_FAILED", e.message, null)
        }
    }
    
    /**
     * Stop VPN service
     */
    private fun stopVpn(result: MethodChannel.Result) {
        try {
            vpnService?.stopVpn()
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop VPN service", e)
            result.error("STOP_FAILED", e.message, null)
        }
    }
    
    /**
     * Get current VPN status
     */
    private fun getVpnStatus(result: MethodChannel.Result) {
        val status = vpnService?.vpnStatus?.value
        if (status != null) {
            val statusMap = mapOf(
                "state" to status.state.name,
                "connectedTime" to status.connectedTime,
                "bytesReceived" to status.bytesReceived,
                "bytesSent" to status.bytesSent,
                "packetsProcessed" to status.packetsProcessed,
                "errorMessage" to status.errorMessage
            )
            result.success(statusMap)
        } else {
            result.success(mapOf("state" to "DISCONNECTED"))
        }
    }
    
    /**
     * Update app rule (block/unblock/disallow)
     */
    private fun updateAppRule(call: MethodCall, result: MethodChannel.Result) {
        val packageName = call.argument<String>("packageName")
        val isBlocked = call.argument<Boolean>("isBlocked") ?: false
        val isDisallowed = call.argument<Boolean>("isDisallowed") ?: false
        
        if (packageName == null) {
            result.error("INVALID_ARGS", "Package name is required", null)
            return
        }
        
        scope.launch {
            try {
                repository.updateAppRule(packageName, isBlocked, isDisallowed)
                result.success(true)
            } catch (e: Exception) {
                result.error("UPDATE_FAILED", e.message, null)
            }
        }
    }
    
    /**
     * Get list of installed apps
     */
    private fun getInstalledApps(result: MethodChannel.Result) {
        scope.launch {
            try {
                val apps = repository.installedApps.value.map { app ->
                    mapOf(
                        "packageName" to app.packageName,
                        "appName" to app.appName,
                        "isSystemApp" to app.isSystemApp,
                        "uid" to app.uid,
                        "versionName" to app.versionName,
                        "versionCode" to app.versionCode,
                        "isBlocked" to app.isBlocked,
                        "isDisallowed" to app.isDisallowed
                    )
                }
                result.success(apps)
            } catch (e: Exception) {
                result.error("LOAD_FAILED", e.message, null)
            }
        }
    }
    
    /**
     * Get current VPN configuration
     */
    private fun getVpnConfig(result: MethodChannel.Result) {
        val config = repository.vpnConfig.value
        val configMap = mapOf(
            "logTraffic" to config.logTraffic,
            "virtualIp" to config.virtualIp,
            "virtualSubnet" to config.virtualSubnet,
            "dnsServers" to config.dnsServers,
            "mtu" to config.mtu,
            "appRules" to config.appRules.map { rule ->
                mapOf(
                    "packageName" to rule.packageName,
                    "isBlocked" to rule.isBlocked,
                    "isDisallowed" to rule.isDisallowed,
                    "description" to rule.description
                )
            }
        )
        result.success(configMap)
    }
    
    /**
     * Update VPN configuration
     */
    private fun updateVpnConfig(call: MethodCall, result: MethodChannel.Result) {
        scope.launch {
            try {
                val logTraffic = call.argument<Boolean>("logTraffic")
                val virtualIp = call.argument<String>("virtualIp")
                val virtualSubnet = call.argument<String>("virtualSubnet")
                val dnsServers = call.argument<List<String>>("dnsServers")
                val mtu = call.argument<Int>("mtu")
                
                repository.updateVpnSettings(logTraffic, virtualIp, virtualSubnet, dnsServers, mtu)
                
                // Update service config if connected
                vpnService?.updateConfig(repository.vpnConfig.value)
                
                result.success(true)
            } catch (e: Exception) {
                result.error("CONFIG_UPDATE_FAILED", e.message, null)
            }
        }
    }
    
    /**
     * Get traffic logs
     */
    private fun getTrafficLogs(result: MethodChannel.Result) {
        val logger = vpnService?.getLogger()
        if (logger != null) {
            val logs = logger.logs.value.map { log ->
                mapOf(
                    "timestamp" to log.timestamp,
                    "sourceIp" to log.sourceIp,
                    "destinationIp" to log.destinationIp,
                    "sourcePort" to log.sourcePort,
                    "destinationPort" to log.destinationPort,
                    "protocol" to log.protocol,
                    "appUid" to log.appUid,
                    "packageName" to log.packageName,
                    "dataSize" to log.dataSize,
                    "isBlocked" to log.isBlocked
                )
            }
            result.success(logs)
        } else {
            result.success(emptyList<Map<String, Any>>())
        }
    }
    
    /**
     * Get traffic statistics
     */
    private fun getTrafficStats(result: MethodChannel.Result) {
        val logger = vpnService?.getLogger()
        if (logger != null) {
            val stats = logger.getTrafficStats()
            val statsMap = mapOf(
                "totalPackets" to stats.totalPackets,
                "blockedPackets" to stats.blockedPackets,
                "allowedPackets" to stats.allowedPackets,
                "totalBytes" to stats.totalBytes,
                "uniqueApps" to stats.uniqueApps,
                "protocolStats" to stats.protocolStats,
                "timeRange" to mapOf(
                    "start" to stats.timeRange.first,
                    "end" to stats.timeRange.second
                )
            )
            result.success(statsMap)
        } else {
            result.success(mapOf<String, Any>())
        }
    }
    
    /**
     * Clear traffic logs
     */
    private fun clearLogs(result: MethodChannel.Result) {
        vpnService?.getLogger()?.clearLogs()
        result.success(true)
    }
    
    /**
     * Export configuration
     */
    private fun exportConfig(result: MethodChannel.Result) {
        val configJson = repository.exportConfig()
        result.success(configJson)
    }
    
    /**
     * Import configuration
     */
    private fun importConfig(call: MethodCall, result: MethodChannel.Result) {
        val configJson = call.argument<String>("config")
        if (configJson == null) {
            result.error("INVALID_ARGS", "Config JSON is required", null)
            return
        }
        
        scope.launch {
            val success = repository.importConfig(configJson)
            if (success) {
                // Update service config if connected
                vpnService?.updateConfig(repository.vpnConfig.value)
                result.success(true)
            } else {
                result.error("IMPORT_FAILED", "Invalid configuration format", null)
            }
        }
    }
    
    /**
     * Search apps
     */
    private fun searchApps(call: MethodCall, result: MethodChannel.Result) {
        val query = call.argument<String>("query") ?: ""
        val apps = repository.searchApps(query).map { app ->
            mapOf(
                "packageName" to app.packageName,
                "appName" to app.appName,
                "isSystemApp" to app.isSystemApp,
                "uid" to app.uid,
                "isBlocked" to app.isBlocked,
                "isDisallowed" to app.isDisallowed
            )
        }
        result.success(apps)
    }
    
    /**
     * Bind to VPN service
     */
    private fun bindToVpnService() {
        val intent = Intent(context, SecurityVpnService::class.java)
        context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }
    
    /**
     * Observe VPN status changes and send to Flutter
     */
    private fun observeVpnStatus() {
        scope.launch {
            vpnService?.vpnStatus?.collect { status ->
                val statusMap = mapOf(
                    "state" to status.state.name,
                    "connectedTime" to status.connectedTime,
                    "bytesReceived" to status.bytesReceived,
                    "bytesSent" to status.bytesSent,
                    "packetsProcessed" to status.packetsProcessed,
                    "errorMessage" to status.errorMessage
                )
                methodChannel.invokeMethod("onVpnStatusChanged", statusMap)
            }
        }
    }
    
    /**
     * Handle VPN permission result
     */
    fun onActivityResult(requestCode: Int, resultCode: Int) {
        if (requestCode == VPN_REQUEST_CODE) {
            val granted = resultCode == Activity.RESULT_OK
            methodChannel.invokeMethod("onVpnPermissionResult", granted)
        }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        scope.cancel()
        if (isServiceBound) {
            context.unbindService(serviceConnection)
            isServiceBound = false
        }
    }
}
