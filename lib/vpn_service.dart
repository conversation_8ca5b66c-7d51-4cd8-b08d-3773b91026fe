import 'dart:convert';
import 'package:flutter/services.dart';

/// Flutter service for interacting with the Android VPN service
class VpnService {
  static const MethodChannel _channel = MethodChannel('com.example.securityapp/vpn');
  
  /// Request VPN permission from the user
  static Future<bool> requestVpnPermission() async {
    try {
      final bool result = await _channel.invokeMethod('requestVpnPermission');
      return result;
    } catch (e) {
      print('Error requesting VPN permission: $e');
      return false;
    }
  }
  
  /// Start the VPN service with optional configuration
  static Future<bool> startVpn({String? config}) async {
    try {
      final bool result = await _channel.invokeMethod('startVpn', {
        'config': config,
      });
      return result;
    } catch (e) {
      print('Error starting VPN: $e');
      return false;
    }
  }
  
  /// Stop the VPN service
  static Future<bool> stopVpn() async {
    try {
      final bool result = await _channel.invokeMethod('stopVpn');
      return result;
    } catch (e) {
      print('Error stopping VPN: $e');
      return false;
    }
  }
  
  /// Get current VPN status
  static Future<Map<String, dynamic>?> getVpnStatus() async {
    try {
      final Map<String, dynamic> result = 
          Map<String, dynamic>.from(await _channel.invokeMethod('getVpnStatus'));
      return result;
    } catch (e) {
      print('Error getting VPN status: $e');
      return null;
    }
  }
  
  /// Update app rule (block/unblock/disallow)
  static Future<bool> updateAppRule({
    required String packageName,
    required bool isBlocked,
    bool isDisallowed = false,
  }) async {
    try {
      final bool result = await _channel.invokeMethod('updateAppRule', {
        'packageName': packageName,
        'isBlocked': isBlocked,
        'isDisallowed': isDisallowed,
      });
      return result;
    } catch (e) {
      print('Error updating app rule: $e');
      return false;
    }
  }
  
  /// Get list of installed apps
  static Future<List<Map<String, dynamic>>> getInstalledApps() async {
    try {
      final List<dynamic> result = await _channel.invokeMethod('getInstalledApps');
      return result.map((app) => Map<String, dynamic>.from(app)).toList();
    } catch (e) {
      print('Error getting installed apps: $e');
      return [];
    }
  }
  
  /// Get current VPN configuration
  static Future<Map<String, dynamic>?> getVpnConfig() async {
    try {
      final Map<String, dynamic> result = 
          Map<String, dynamic>.from(await _channel.invokeMethod('getVpnConfig'));
      return result;
    } catch (e) {
      print('Error getting VPN config: $e');
      return null;
    }
  }
  
  /// Update VPN configuration
  static Future<bool> updateVpnConfig({
    bool? logTraffic,
    String? virtualIp,
    String? virtualSubnet,
    List<String>? dnsServers,
    int? mtu,
  }) async {
    try {
      final Map<String, dynamic> params = {};
      if (logTraffic != null) params['logTraffic'] = logTraffic;
      if (virtualIp != null) params['virtualIp'] = virtualIp;
      if (virtualSubnet != null) params['virtualSubnet'] = virtualSubnet;
      if (dnsServers != null) params['dnsServers'] = dnsServers;
      if (mtu != null) params['mtu'] = mtu;
      
      final bool result = await _channel.invokeMethod('updateVpnConfig', params);
      return result;
    } catch (e) {
      print('Error updating VPN config: $e');
      return false;
    }
  }
  
  /// Get traffic logs
  static Future<List<Map<String, dynamic>>> getTrafficLogs() async {
    try {
      final List<dynamic> result = await _channel.invokeMethod('getTrafficLogs');
      return result.map((log) => Map<String, dynamic>.from(log)).toList();
    } catch (e) {
      print('Error getting traffic logs: $e');
      return [];
    }
  }
  
  /// Get traffic statistics
  static Future<Map<String, dynamic>?> getTrafficStats() async {
    try {
      final Map<String, dynamic> result = 
          Map<String, dynamic>.from(await _channel.invokeMethod('getTrafficStats'));
      return result;
    } catch (e) {
      print('Error getting traffic stats: $e');
      return null;
    }
  }
  
  /// Clear traffic logs
  static Future<bool> clearLogs() async {
    try {
      final bool result = await _channel.invokeMethod('clearLogs');
      return result;
    } catch (e) {
      print('Error clearing logs: $e');
      return false;
    }
  }
  
  /// Export configuration as JSON
  static Future<String?> exportConfig() async {
    try {
      final String result = await _channel.invokeMethod('exportConfig');
      return result;
    } catch (e) {
      print('Error exporting config: $e');
      return null;
    }
  }
  
  /// Import configuration from JSON
  static Future<bool> importConfig(String config) async {
    try {
      final bool result = await _channel.invokeMethod('importConfig', {
        'config': config,
      });
      return result;
    } catch (e) {
      print('Error importing config: $e');
      return false;
    }
  }
  
  /// Search apps by name or package name
  static Future<List<Map<String, dynamic>>> searchApps(String query) async {
    try {
      final List<dynamic> result = await _channel.invokeMethod('searchApps', {
        'query': query,
      });
      return result.map((app) => Map<String, dynamic>.from(app)).toList();
    } catch (e) {
      print('Error searching apps: $e');
      return [];
    }
  }
  
  /// Set up method call handler for receiving updates from native side
  static void setMethodCallHandler(Function(MethodCall) handler) {
    _channel.setMethodCallHandler(handler);
  }
}

/// VPN Status enum
enum VpnState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}

/// Helper class for VPN status
class VpnStatus {
  final VpnState state;
  final int connectedTime;
  final int bytesReceived;
  final int bytesSent;
  final int packetsProcessed;
  final String? errorMessage;
  
  VpnStatus({
    required this.state,
    required this.connectedTime,
    required this.bytesReceived,
    required this.bytesSent,
    required this.packetsProcessed,
    this.errorMessage,
  });
  
  factory VpnStatus.fromMap(Map<String, dynamic> map) {
    return VpnStatus(
      state: _parseVpnState(map['state'] as String),
      connectedTime: map['connectedTime'] as int? ?? 0,
      bytesReceived: map['bytesReceived'] as int? ?? 0,
      bytesSent: map['bytesSent'] as int? ?? 0,
      packetsProcessed: map['packetsProcessed'] as int? ?? 0,
      errorMessage: map['errorMessage'] as String?,
    );
  }
  
  static VpnState _parseVpnState(String state) {
    switch (state.toLowerCase()) {
      case 'connected':
        return VpnState.connected;
      case 'connecting':
        return VpnState.connecting;
      case 'disconnecting':
        return VpnState.disconnecting;
      case 'error':
        return VpnState.error;
      default:
        return VpnState.disconnected;
    }
  }
}

/// Helper class for app information
class AppInfo {
  final String packageName;
  final String appName;
  final bool isSystemApp;
  final int uid;
  final String? versionName;
  final int versionCode;
  final bool isBlocked;
  final bool isDisallowed;
  
  AppInfo({
    required this.packageName,
    required this.appName,
    required this.isSystemApp,
    required this.uid,
    this.versionName,
    required this.versionCode,
    required this.isBlocked,
    required this.isDisallowed,
  });
  
  factory AppInfo.fromMap(Map<String, dynamic> map) {
    return AppInfo(
      packageName: map['packageName'] as String,
      appName: map['appName'] as String,
      isSystemApp: map['isSystemApp'] as bool,
      uid: map['uid'] as int,
      versionName: map['versionName'] as String?,
      versionCode: map['versionCode'] as int,
      isBlocked: map['isBlocked'] as bool,
      isDisallowed: map['isDisallowed'] as bool,
    );
  }
}
