import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'vpn_service.dart';

void main() {
  runApp(const AppSentryApp());
}

class AppSentryApp extends StatelessWidget {
  const AppSentryApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AppSentry VPN',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const VpnHomePage(),
    );
  }
}

class VpnHomePage extends StatefulWidget {
  const VpnHomePage({super.key});

  @override
  State<VpnHomePage> createState() => _VpnHomePageState();
}

class _VpnHomePageState extends State<VpnHomePage> {
  VpnStatus? _vpnStatus;
  List<AppInfo> _installedApps = [];
  List<Map<String, dynamic>> _trafficLogs = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _setupVpnService();
    _loadInstalledApps();
    _loadVpnStatus();
  }

  void _setupVpnService() {
    // Set up method call handler for receiving updates from native side
    VpnService.setMethodCallHandler((MethodCall call) async {
      switch (call.method) {
        case 'onVpnStatusChanged':
          final statusMap = Map<String, dynamic>.from(call.arguments);
          setState(() {
            _vpnStatus = VpnStatus.fromMap(statusMap);
          });
          break;
        case 'onVpnPermissionResult':
          final granted = call.arguments as bool;
          if (granted) {
            _startVpn();
          } else {
            _showSnackBar('VPN permission denied');
          }
          break;
      }
    });
  }

  Future<void> _loadInstalledApps() async {
    setState(() => _isLoading = true);
    try {
      final apps = await VpnService.getInstalledApps();
      setState(() {
        _installedApps = apps.map((app) => AppInfo.fromMap(app)).toList();
      });
    } catch (e) {
      _showSnackBar('Failed to load apps: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadVpnStatus() async {
    try {
      final status = await VpnService.getVpnStatus();
      if (status != null) {
        setState(() {
          _vpnStatus = VpnStatus.fromMap(status);
        });
      }
    } catch (e) {
      _showSnackBar('Failed to load VPN status: $e');
    }
  }

  Future<void> _requestVpnPermission() async {
    final granted = await VpnService.requestVpnPermission();
    if (granted) {
      _startVpn();
    }
  }

  Future<void> _startVpn() async {
    setState(() => _isLoading = true);
    try {
      final success = await VpnService.startVpn();
      if (success) {
        _showSnackBar('VPN started successfully');
      } else {
        _showSnackBar('Failed to start VPN');
      }
    } catch (e) {
      _showSnackBar('Error starting VPN: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _stopVpn() async {
    setState(() => _isLoading = true);
    try {
      final success = await VpnService.stopVpn();
      if (success) {
        _showSnackBar('VPN stopped successfully');
      } else {
        _showSnackBar('Failed to stop VPN');
      }
    } catch (e) {
      _showSnackBar('Error stopping VPN: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _toggleAppBlock(AppInfo app) async {
    try {
      final success = await VpnService.updateAppRule(
        packageName: app.packageName,
        isBlocked: !app.isBlocked,
        isDisallowed: app.isDisallowed,
      );
      if (success) {
        _loadInstalledApps(); // Refresh the list
        _showSnackBar('${app.appName} ${!app.isBlocked ? 'blocked' : 'unblocked'}');
      } else {
        _showSnackBar('Failed to update app rule');
      }
    } catch (e) {
      _showSnackBar('Error updating app rule: $e');
    }
  }

  Future<void> _loadTrafficLogs() async {
    try {
      final logs = await VpnService.getTrafficLogs();
      setState(() {
        _trafficLogs = logs;
      });
    } catch (e) {
      _showSnackBar('Failed to load traffic logs: $e');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('AppSentry VPN'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          bottom: const TabBar(
            tabs: [
              Tab(icon: Icon(Icons.vpn_key), text: 'VPN'),
              Tab(icon: Icon(Icons.apps), text: 'Apps'),
              Tab(icon: Icon(Icons.list), text: 'Logs'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            _buildVpnTab(),
            _buildAppsTab(),
            _buildLogsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildVpnTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    _vpnStatus?.state == VpnState.connected
                        ? Icons.vpn_lock
                        : Icons.vpn_key_off,
                    size: 64,
                    color: _vpnStatus?.state == VpnState.connected
                        ? Colors.green
                        : Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _vpnStatus?.state.name.toUpperCase() ?? 'DISCONNECTED',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  if (_vpnStatus?.state == VpnState.connected) ...[
                    Text('Connected for: ${_formatDuration(_vpnStatus!.connectedTime)}'),
                    Text('Bytes received: ${_formatBytes(_vpnStatus!.bytesReceived)}'),
                    Text('Bytes sent: ${_formatBytes(_vpnStatus!.bytesSent)}'),
                    Text('Packets processed: ${_vpnStatus!.packetsProcessed}'),
                  ],
                  const SizedBox(height: 16),
                  if (_isLoading)
                    const CircularProgressIndicator()
                  else
                    ElevatedButton(
                      onPressed: _vpnStatus?.state == VpnState.connected
                          ? _stopVpn
                          : _requestVpnPermission,
                      child: Text(
                        _vpnStatus?.state == VpnState.connected
                            ? 'Stop VPN'
                            : 'Start VPN',
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView.builder(
      itemCount: _installedApps.length,
      itemBuilder: (context, index) {
        final app = _installedApps[index];
        return ListTile(
          leading: Icon(
            app.isSystemApp ? Icons.android : Icons.apps,
            color: app.isBlocked ? Colors.red : Colors.green,
          ),
          title: Text(app.appName),
          subtitle: Text(app.packageName),
          trailing: Switch(
            value: app.isBlocked,
            onChanged: (_) => _toggleAppBlock(app),
          ),
        );
      },
    );
  }

  Widget _buildLogsTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: ElevatedButton(
            onPressed: _loadTrafficLogs,
            child: const Text('Refresh Logs'),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _trafficLogs.length,
            itemBuilder: (context, index) {
              final log = _trafficLogs[index];
              return ListTile(
                leading: Icon(
                  log['isBlocked'] ? Icons.block : Icons.check_circle,
                  color: log['isBlocked'] ? Colors.red : Colors.green,
                ),
                title: Text('${log['sourceIp']}:${log['sourcePort']} → ${log['destinationIp']}:${log['destinationPort']}'),
                subtitle: Text('${log['protocol']} | ${log['packageName']} | ${log['dataSize']} bytes'),
                trailing: Text(_formatTime(log['timestamp'])),
              );
            },
          ),
        ),
      ],
    );
  }

  String _formatDuration(int milliseconds) {
    final duration = Duration(milliseconds: milliseconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatTime(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}:${date.second.toString().padLeft(2, '0')}';
  }
}
